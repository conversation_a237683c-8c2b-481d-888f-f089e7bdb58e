# ⚠️ Error Handling System Guide

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Error Flow Architecture](#-error-flow-architecture)
3. [Retry Integration](#-retry-integration)
4. [Failure Types](#-failure-types)
5. [Error Interceptor](#-error-interceptor)
6. [Error Message Mapping](#-error-message-mapping)
7. [Best Practices](#-best-practices)
8. [Testing Error Scenarios](#-testing-error-scenarios)

## 🎯 Overview

The error handling system provides comprehensive error management with automatic retry capabilities, structured failure types, and localized error messages. It follows Clean Architecture principles with clear separation between network errors, domain failures, and presentation messages.

## 🏗️ Error Flow Architecture

### Complete Error Flow with Retry

```
API Request → SimpleRetryInterceptor → ErrorInterceptor → Domain Failure → Presentation Message
     ↓              ↓                      ↓                ↓               ↓
Network Error → Retry Logic → Transform Error → Map to Failure → Show User Message
```

### Detailed Flow

1. **API Request**: Made through BaseApiService
2. **SimpleRetryInterceptor**: Handles retryable errors automatically
3. **ErrorInterceptor**: Transforms remaining errors to domain failures
4. **Repository**: Returns Either<Failure, Data>
5. **Presentation**: Maps failures to user-friendly messages

## 🔄 Retry Integration

### How Retry Affects Error Handling

#### Before Retry System
```
Network Error → ErrorInterceptor → Failure → User sees error immediately
```

#### With Retry System
```
Network Error → SimpleRetryInterceptor → Retry attempts → Final error → ErrorInterceptor → Failure
```

### Retry-Error Interaction

#### Retryable Errors (Handled by SimpleRetryInterceptor)
- ✅ Connection timeout → Retry → Success (no error to user)
- ✅ Server error (5xx) → Retry → Success (no error to user)
- ✅ Network issues → Retry → Final failure → ErrorInterceptor

#### Non-Retryable Errors (Go directly to ErrorInterceptor)
- ❌ Client errors (4xx) → ErrorInterceptor immediately
- ❌ Authentication errors → ErrorInterceptor immediately
- ❌ Validation errors → ErrorInterceptor immediately

### Error Logging with Retry

```
// Retry attempts are logged
SimpleRetryInterceptor: Retrying attempt 1/3 after 1000ms
SimpleRetryInterceptor: Retry failed, attempting 2/3 after 2000ms
SimpleRetryInterceptor: Max retries exceeded: 3/3

// Final error is processed
ErrorInterceptor: Transforming DioException to ServerFailure
```

## 📊 Failure Types

### Structured Failure Hierarchy

```dart
@freezed
class Failure with _$Failure {
  const factory Failure.server(
    ServerErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ServerFailure;

  const factory Failure.network(
    NetworkErrorType errorType,
  ) = NetworkFailure;

  const factory Failure.auth(
    AuthErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = AuthFailure;

  const factory Failure.validation(
    ValidationErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ValidationFailure;

  const factory Failure.cache(
    CacheErrorType errorType,
  ) = CacheFailure;
}
```

### Error Type Categories

#### ServerErrorType
- `internalError` - 500, 502, 503, 504 (retryable)
- `notFound` - 404 (non-retryable)
- `tooManyRequests` - 429 (retryable)
- `badRequest` - 400 (non-retryable)

#### NetworkErrorType
- `connectionTimeout` - Connection timeout (retryable)
- `sendTimeout` - Send timeout (retryable)
- `receiveTimeout` - Receive timeout (retryable)
- `noConnection` - No internet (non-retryable)
- `requestCancelled` - Cancelled request (non-retryable)

#### AuthErrorType
- `invalidCredentials` - 401 (non-retryable)
- `accessDenied` - 403 (non-retryable)
- `tokenExpired` - Token expired (non-retryable)

## 🔧 Error Interceptor

### Error Transformation Logic

```dart
@injectable
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Transform DioException to custom failure types
    final failure = _mapDioExceptionToFailure(err);

    // Create new DioException with failure information
    final customError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: failure, // Domain failure attached here
    );

    handler.next(customError);
  }
}
```

### Status Code Mapping

| HTTP Status | Failure Type | Retryable | Retry Behavior |
|-------------|--------------|-----------|----------------|
| 400 | ValidationFailure | ❌ | Goes directly to ErrorInterceptor |
| 401 | AuthFailure | ❌ | Goes directly to ErrorInterceptor |
| 403 | AuthFailure | ❌ | Goes directly to ErrorInterceptor |
| 404 | ServerFailure | ❌ | Goes directly to ErrorInterceptor |
| 422 | ValidationFailure | ❌ | Goes directly to ErrorInterceptor |
| 429 | ServerFailure | ✅ | Retried by SimpleRetryInterceptor |
| 500-504 | ServerFailure | ✅ | Retried by SimpleRetryInterceptor |

### Network Error Mapping

| DioExceptionType | Failure Type | Retryable | Retry Behavior |
|------------------|--------------|-----------|----------------|
| connectionTimeout | NetworkFailure | ✅ | Retried automatically |
| sendTimeout | NetworkFailure | ✅ | Retried automatically |
| receiveTimeout | NetworkFailure | ✅ | Retried automatically |
| connectionError | NetworkFailure | ✅ | Retried automatically |
| cancel | NetworkFailure | ❌ | Goes directly to ErrorInterceptor |

## 🗺️ Error Message Mapping

### Presentation Layer Mapping

```dart
class ErrorMessageMapper {
  static String getErrorMessage(BuildContext context, Failure failure) {
    final localizations = S.of(context);
    
    return failure.when(
      server: (errorType, serverMessage, serverErrorCode) => 
          _getServerErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
      network: (errorType) => _getNetworkErrorMessage(localizations, errorType),
      auth: (errorType, serverMessage, serverErrorCode) => 
          _getAuthErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
      validation: (errorType, serverMessage, serverErrorCode) => 
          _getValidationErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
      cache: (errorType) => _getCacheErrorMessage(localizations, errorType),
    );
  }
}
```

### Error Message Format

All error messages follow the format: `message (error code)`

Examples:
- `"Server temporarily unavailable (503)"`
- `"Invalid email format (INVALID_EMAIL)"`
- `"Connection timeout (TIMEOUT)"`

### Retry-Aware Error Messages

```dart
// Network errors after retry exhaustion
case NetworkErrorType.connectionTimeout:
  return "${localizations.errorNetworkTimeout} (${ErrorCodes.networkTimeout})";
  // "Connection failed after multiple attempts (NETWORK_TIMEOUT)"

// Server errors after retry exhaustion  
case ServerErrorType.internalError:
  return "${localizations.errorServerInternal} (${ErrorCodes.serverInternal})";
  // "Server temporarily unavailable (SERVER_ERROR)"
```

## 📋 Best Practices

### 1. Repository Implementation

```dart
@Injectable(as: UserRepository)
class UserRepositoryImpl implements UserRepository {
  final UserApiService _apiService;
  final NetworkInfo _networkInfo;

  @override
  Future<Either<Failure, List<User>>> getUsers() async {
    // Check network connectivity first
    if (!await _networkInfo.isConnected) {
      return const Left(NetworkFailure(NetworkErrorType.noConnection));
    }

    // API call with automatic retry and error handling
    return await _apiService.getUsers();
  }
}
```

### 2. Presentation Layer Error Handling

```dart
class UserScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersState = ref.watch(usersControllerProvider);

    return usersState.when(
      data: (users) => UsersList(users: users),
      loading: () => const LoadingIndicator(),
      error: (error, stackTrace) {
        // Handle error with retry context
        final errorMessage = _getErrorMessage(context, error);
        return ErrorWidget(
          message: errorMessage,
          onRetry: () => ref.refresh(usersControllerProvider),
        );
      },
    );
  }

  String _getErrorMessage(BuildContext context, Object error) {
    if (error is DioException && error.error is Failure) {
      return ErrorMessageMapper.getErrorMessage(context, error.error as Failure);
    }
    return S.of(context).errorGeneric;
  }
}
```

### 3. Controller Error Handling

```dart
@riverpod
class UsersController extends _$UsersController {
  @override
  Future<List<User>> build() async {
    final result = await ref.read(userRepositoryProvider).getUsers();
    
    return result.fold(
      (failure) {
        // Log error for debugging
        AppLogger.error('Failed to load users', error: failure);
        
        // Throw error to be handled by AsyncValue
        throw DioException(
          requestOptions: RequestOptions(path: '/users'),
          error: failure,
        );
      },
      (users) => users,
    );
  }

  Future<void> refreshUsers() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => build());
  }
}
```

## 🧪 Testing Error Scenarios

### Unit Testing with Mock Errors

```dart
group('Error Handling with Retry', () {
  test('should retry network errors and eventually fail', () async {
    // Mock network error that will be retried
    when(mockApiService.getUsers()).thenThrow(
      DioException(
        requestOptions: RequestOptions(path: '/users'),
        type: DioExceptionType.connectionTimeout,
      ),
    );

    final result = await repository.getUsers();

    // Verify retry attempts were made
    verify(mockApiService.getUsers()).called(4); // 1 initial + 3 retries
    
    // Verify final failure
    expect(result.isLeft(), true);
    result.fold(
      (failure) => expect(failure, isA<NetworkFailure>()),
      (data) => fail('Should have failed'),
    );
  });

  test('should not retry client errors', () async {
    // Mock client error that should not be retried
    when(mockApiService.getUsers()).thenThrow(
      DioException(
        requestOptions: RequestOptions(path: '/users'),
        response: Response(
          requestOptions: RequestOptions(path: '/users'),
          statusCode: 400,
        ),
        type: DioExceptionType.badResponse,
      ),
    );

    final result = await repository.getUsers();

    // Verify no retry attempts
    verify(mockApiService.getUsers()).called(1); // Only initial call
    
    // Verify immediate failure
    expect(result.isLeft(), true);
  });
});
```

### Integration Testing with Mock APIs

```dart
testWidgets('should show retry-aware error messages', (tester) async {
  // Enable mock mode with error simulation
  AppConstants.isMockMode = true;
  MockAuthInterceptor.simulateNetworkError = true;

  await tester.pumpWidget(MyApp());
  
  // Trigger API call
  await tester.tap(find.byKey(Key('load_users_button')));
  await tester.pump();

  // Wait for retry attempts to complete
  await tester.pump(Duration(seconds: 10));

  // Verify error message shows retry context
  expect(
    find.text('Connection failed after multiple attempts (NETWORK_TIMEOUT)'),
    findsOneWidget,
  );
});
```

### Manual Testing Scenarios

1. **Network Timeout Simulation**
   - Disable WiFi during API call
   - Verify retry attempts in logs
   - Check final error message

2. **Server Error Simulation**
   - Use mock API with 500 error
   - Verify retry behavior
   - Check error message format

3. **Client Error Testing**
   - Use invalid credentials
   - Verify no retry attempts
   - Check immediate error display

## 🔍 Debugging Error Handling

### Enable Comprehensive Logging

```dart
// Enable retry logging
final retryInterceptor = SimpleRetryInterceptor(enableLogging: true);

// Enable error interceptor logging (add to ErrorInterceptor)
AppLogger.debug('ErrorInterceptor: Processing ${err.type} error');
AppLogger.debug('ErrorInterceptor: Status code ${err.response?.statusCode}');
AppLogger.debug('ErrorInterceptor: Mapped to ${failure.runtimeType}');
```

### Log Output Example

```
SimpleRetryInterceptor: Retrying attempt 1/3 after 1000ms
SimpleRetryInterceptor: Retry failed, attempting 2/3 after 2000ms
SimpleRetryInterceptor: Max retries exceeded: 3/3
ErrorInterceptor: Processing connectionTimeout error
ErrorInterceptor: Mapped to NetworkFailure
ErrorMessageMapper: Showing message "Connection failed after multiple attempts (NETWORK_TIMEOUT)"
```

---

**This guide provides comprehensive coverage of error handling with retry integration. The system ensures reliable error recovery while providing clear feedback to users when all retry attempts are exhausted.**
