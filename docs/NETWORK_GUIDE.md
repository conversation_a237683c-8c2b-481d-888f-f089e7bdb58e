# 🌐 Network Layer Complete Guide

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Architecture](#-architecture)
3. [API Clients](#-api-clients)
4. [Interceptor System](#-interceptor-system)
5. [Retry System](#-retry-system)
6. [Mock API System](#-mock-api-system)
7. [<PERSON>rror Handling](#-error-handling)
8. [OpenAPI Integration](#-openapi-integration)
9. [Best Practices](#-best-practices)
10. [Troubleshooting](#-troubleshooting)

## 🎯 Overview

The network layer provides a comprehensive, clean architecture-compliant solution for API communication with:

- **Multiple API Clients**: Different configurations for different use cases
- **Simple Retry System**: Automatic retry logic for transient failures
- **Mock API System**: Complete mock implementation for development
- **Interceptor Chain**: Modular request/response processing
- **OpenAPI Ready**: Prepared for future code generation
- **Error Handling**: Consistent error management across all APIs

## 🏗️ Architecture

### Directory Structure

```
lib/data/network/
├── README.md                           # Network layer documentation
├── api_client.dart                     # Multiple Dio instances management
├── base_api_service.dart              # Base class for manual APIs
├── dio_builder.dart                   # Factory for configured Dio instances
├── open_api_client.dart               # Wrapper for future generated APIs
└── interceptors/                      # Request/response interceptors
    ├── auth_interceptor.dart          # Authentication interceptor
    ├── error_interceptor.dart         # Error handling interceptor
    ├── logging_interceptor.dart       # Request/response logging
    ├── simple_retry_interceptor.dart  # Simple automatic retry logic
    ├── README_RETRY.md                # Retry system documentation
    └── mock/                          # Mock API system
        ├── README.md                  # Mock system documentation
        ├── base_mock_interceptor.dart # Base class for mock interceptors
        ├── mock_auth_interceptor.dart # Auth mock implementation
        └── test_mock_interceptors.dart # Test utilities
```

### Key Components

1. **ApiClient**: Manages multiple Dio instances with different configurations
2. **BaseApiService**: Provides consistent error handling with Either pattern
3. **DioBuilder**: Factory for creating configured Dio instances
4. **Interceptors**: Modular request/response processing pipeline
5. **Mock System**: Complete mock API implementation for development

## 🔧 API Clients

### Available API Clients

#### Default API Client
```dart
@Named('default') ApiClient defaultApiClient;
```
- **Purpose**: Most authenticated API requests
- **Retry Policy**: Environment-based (dev: 5 retries, prod: 3 retries)
- **Interceptors**: Logging → Retry → Mock → Auth → Error
- **Use Case**: Standard business API calls

#### Auth API Client
```dart
@Named('auth') ApiClient authApiClient;
```
- **Purpose**: Authentication endpoints (login, logout, register)
- **Retry Policy**: Conservative (2 retries, longer delay)
- **Interceptors**: Logging → Retry → Mock → Error
- **Use Case**: Authentication operations

#### Public API Client
```dart
@Named('public') ApiClient publicApiClient;
```
- **Purpose**: Public endpoints without authentication
- **Retry Policy**: Standard (3 retries, exponential backoff)
- **Interceptors**: Logging → Retry → Error
- **Use Case**: Public data, app configuration

#### Upload API Client
```dart
@Named('upload') ApiClient uploadApiClient;
```
- **Purpose**: File upload operations
- **Retry Policy**: No retry (large files can be time-consuming)
- **Interceptors**: Logging → Mock → Auth → Error
- **Use Case**: File uploads, media operations

### Usage Example

```dart
@injectable
class AuthApiService extends BaseApiService {
  final ApiClient _apiClient;

  AuthApiService(@Named('auth') this._apiClient) : super(_apiClient);

  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post('/auth/login', data: request.toJson()),
      (data) => LoginResponse.fromJson(data),
    );
  }
}
```

## 🔄 Interceptor System

### Interceptor Order

The interceptors are applied in the following order for optimal functionality:

1. **LoggingInterceptor** - Request/response logging
2. **SimpleRetryInterceptor** - Automatic retry logic
3. **MockInterceptor** - Mock API responses (development only)
4. **AuthInterceptor** - Authentication headers
5. **ErrorInterceptor** - Error handling and transformation

### Core Interceptors

#### LoggingInterceptor
- **Purpose**: Logs all requests and responses
- **Environment**: Always enabled
- **Features**: Request/response details, timing, error logging

#### SimpleRetryInterceptor
- **Purpose**: Automatic retry for transient failures
- **Configuration**: Environment-based policies
- **Features**: Exponential backoff, configurable retry conditions

#### AuthInterceptor
- **Purpose**: Adds authentication headers
- **Behavior**: Always adds access token when available
- **Features**: Automatic token injection, no conditional logic

#### ErrorInterceptor
- **Purpose**: Transforms errors to domain failures
- **Features**: Status code mapping, error message extraction

#### MockInterceptor
- **Purpose**: Provides mock API responses
- **Environment**: Development only
- **Features**: Realistic delays, error simulation, test data

## 🔄 Retry System

### Simple Retry Approach

The retry system uses a simple, proven approach:

```dart
// Default configuration
SimpleRetryInterceptor()
// - maxRetries: 3
// - initialDelay: 1000ms
// - strategy: exponential backoff with 30s max delay

// Development configuration
SimpleRetryInterceptor.development()
// - maxRetries: 5
// - initialDelay: 500ms
// - More aggressive for testing

// Auth configuration
SimpleRetryInterceptor.auth()
// - maxRetries: 2
// - initialDelay: 1500ms
// - Conservative for auth endpoints
```

### Retryable Conditions

#### ✅ Retryable
- Connection timeout
- Send timeout
- Receive timeout
- Connection errors
- Server errors (5xx status codes)

#### ❌ Non-Retryable
- Cancelled requests
- Client errors (4xx status codes)
- Authentication failures
- Request validation errors

### Monitoring

```
SimpleRetryInterceptor: Retrying attempt 1/3 after 1000ms
SimpleRetryInterceptor: Retry successful on attempt 1
SimpleRetryInterceptor: Max retries exceeded: 3/3
```

## 🎭 Mock API System

### Mock Mode Configuration

```dart
// Enable mock mode
AppConstants.isMockMode = true;
```

### Available Mock APIs

#### Auth APIs
- `POST /auth/login` - User authentication
- `POST /auth/logout` - User logout
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Token refresh

#### Mock Users
```dart
// Test accounts
email: "<EMAIL>", password: "admin123"
email: "<EMAIL>", password: "user123"
email: "<EMAIL>", password: "demo123"
```

### Network Simulation
- **Realistic delays**: 500ms - 2s random delay
- **Error simulation**: 10% chance of network error (optional)
- **Proper HTTP status codes**: 200, 401, 404, 500, etc.
- **API Paths**: Uses `ApiPaths` constants

## ⚠️ Error Handling

### Error Flow with Retry Integration

```
API Error → SimpleRetryInterceptor → ErrorInterceptor → Domain Failure → Presentation Error Message
     ↓              ↓                      ↓                ↓               ↓
Network Error → Retry Logic → Transform Error → Map to Failure → Show User Message
```

### Detailed Error Flow

1. **API Request**: Made through BaseApiService
2. **SimpleRetryInterceptor**: Handles retryable errors automatically
3. **ErrorInterceptor**: Transforms remaining errors to domain failures
4. **Repository**: Returns Either<Failure, Data>
5. **Presentation**: Maps failures to user-friendly messages

### Retry-Error Interaction

#### Retryable Errors (Handled by SimpleRetryInterceptor)
- ✅ Connection timeout → Retry → Success (no error to user)
- ✅ Server error (5xx) → Retry → Success (no error to user)
- ✅ Network issues → Retry → Final failure → ErrorInterceptor

#### Non-Retryable Errors (Go directly to ErrorInterceptor)
- ❌ Client errors (4xx) → ErrorInterceptor immediately
- ❌ Authentication errors → ErrorInterceptor immediately
- ❌ Validation errors → ErrorInterceptor immediately

### Failure Types

```dart
@freezed
class Failure with _$Failure {
  const factory Failure.server(
    ServerErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ServerFailure;

  const factory Failure.network(
    NetworkErrorType errorType,
  ) = NetworkFailure;

  const factory Failure.auth(
    AuthErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = AuthFailure;

  const factory Failure.validation(
    ValidationErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ValidationFailure;

  const factory Failure.cache(
    CacheErrorType errorType,
  ) = CacheFailure;
}
```

### Error Message Format

All errors display as: `message (error code)`

Examples:
- `"Server temporarily unavailable (503)"` (after retry attempts)
- `"Connection failed after multiple attempts (NETWORK_TIMEOUT)"` (after retry attempts)
- `"Invalid email format (INVALID_EMAIL)"` (immediate validation error)

## 🚀 OpenAPI Integration

### Current Manual API Services

```dart
class AuthApiService extends BaseApiService {
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post('/auth/login', data: request.toJson()),
      (data) => LoginResponse.fromJson(data),
    );
  }
}
```

### Future OpenAPI Generated Clients

```dart
// Generated API client will automatically inherit all interceptors
final apiClient = ApiClient(
  interceptors: [
    loggingInterceptor,
    retryInterceptor,    // Same retry logic
    authInterceptor,
    errorInterceptor,
  ],
);

final generatedApi = DefaultApi(apiClient.dio);
```

### Migration Benefits

- **Zero Repository Changes**: Repository interfaces remain unchanged
- **Same Error Handling**: Consistent failure types and error flow
- **Same Retry Logic**: Automatic retry behavior preserved
- **Same Mock System**: Mock interceptors continue to work

## 📋 Best Practices

### 1. API Service Implementation

```dart
@injectable
class UserApiService extends BaseApiService {
  UserApiService(@Named('default') ApiClient apiClient) : super(apiClient);

  Future<Either<Failure, List<User>>> getUsers() async {
    return handleApiCall(
      () => dio.get(ApiPaths.users),
      (data) => (data as List).map((json) => User.fromJson(json)).toList(),
    );
  }
}
```

### 2. Repository Implementation

```dart
@Injectable(as: UserRepository)
class UserRepositoryImpl implements UserRepository {
  final UserApiService _apiService;
  final NetworkInfo _networkInfo;

  UserRepositoryImpl(this._apiService, this._networkInfo);

  @override
  Future<Either<Failure, List<User>>> getUsers() async {
    if (!await _networkInfo.isConnected) {
      return const Left(NetworkFailure('No internet connection'));
    }

    return await _apiService.getUsers();
  }
}
```

### 3. Error Handling in Presentation

```dart
final result = await userRepository.getUsers();

result.fold(
  (failure) {
    // Handle different failure types
    final message = switch (failure) {
      NetworkFailure() => 'Check your internet connection',
      ServerFailure() => 'Server error occurred',
      AuthFailure() => 'Authentication required',
      _ => 'An unexpected error occurred',
    };
    
    showErrorSnackBar(message);
  },
  (users) {
    // Handle success
    updateUsersList(users);
  },
);
```

### 4. Testing with Mock APIs

```dart
void main() {
  setUpAll(() {
    // Enable mock mode for tests
    AppConstants.isMockMode = true;
  });

  testWidgets('should login successfully with mock API', (tester) async {
    // Test will use mock interceptors automatically
    final result = await authRepository.login(
      email: '<EMAIL>',
      password: 'admin123',
    );

    expect(result.isRight(), true);
  });
}
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Retry Not Working
```dart
// Check interceptor order in NetworkModule
final retryInterceptor = SimpleRetryInterceptor();
// Should be early in the chain, before auth/error interceptors
```

#### 2. Mock APIs Not Responding
```dart
// Verify mock mode is enabled
AppConstants.isMockMode = true;

// Check if mock interceptor is registered
// Should be before auth interceptor in the chain
```

#### 3. Authentication Issues
```dart
// Verify AuthInterceptor is after MockInterceptor
// Mock should handle auth endpoints, not AuthInterceptor
```

#### 4. Error Handling Not Working
```dart
// Ensure ErrorInterceptor is last in the chain
// It should process errors after all other interceptors
```

### Debug Logging

Enable detailed logging for debugging:

```dart
// Enable retry logging
final retryInterceptor = SimpleRetryInterceptor(enableLogging: true);

// Check logs for retry attempts
// SimpleRetryInterceptor: Retrying attempt 1/3 after 1000ms
```

### Performance Monitoring

Monitor network performance:

```dart
// Check request timing in logs
// LoggingInterceptor: POST /auth/login completed in 1234ms

// Monitor retry patterns
// Too many retries might indicate server issues
```

---

**This comprehensive guide covers all aspects of the network layer. Follow these patterns and practices to maintain consistency and reliability in your API integration.**
