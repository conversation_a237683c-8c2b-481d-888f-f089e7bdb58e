# 🔄 Token Refresh System Guide

## 📋 Overview

The Token Refresh System automatically handles access token expiration by intercepting 401 Unauthorized responses and attempting to refresh the token using the stored refresh token. This provides a seamless user experience without requiring manual re-authentication.

## 🏗️ Architecture

### Core Components

1. **TokenRefreshInterceptor** - Main interceptor that handles token refresh logic
2. **AuthApiService** - Provides refresh token API endpoint
3. **StorageService** - Manages token storage and expiry tracking
4. **Mock Support** - Mock refresh token endpoint for development

### Flow Diagram

```
API Request → AuthInterceptor (adds token) → API Call
     ↓ (401 Response)
TokenRefreshInterceptor → Check if auth endpoint → Skip if auth endpoint
     ↓ (Non-auth endpoint)
Refresh Token API → Save new tokens → Retry original request
     ↓ (Success)
Return successful response to caller
     ↓ (Refresh fails)
Clear tokens → Return 401 error → Trigger logout
```

## 🔧 Implementation Details

### TokenRefreshInterceptor

**Location**: `lib/data/network/interceptors/token_refresh_interceptor.dart`

**Key Features**:
- **Proactive Refresh**: Check and refresh tokens before requests (30s buffer)
- **Reactive Refresh**: Handle 401 errors with automatic token refresh
- **Thread-safe**: Concurrent request handling with mutex pattern
- **Automatic Retry**: Retry original request with new token
- **Fallback Logic**: Logout on refresh failure
- **Loop Prevention**: Skip refresh for auth endpoints

### Proactive vs Reactive Refresh

#### 🚀 **Proactive Refresh (Primary Strategy)**
- **When**: Before making any API request
- **Trigger**: Token expires within 30 seconds
- **Benefit**: Prevents 401 errors, smoother UX
- **Implementation**: `onRequest()` interceptor method

```dart
@override
void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
  final isExpired = await _storageService.isTokenExpired();
  if (isExpired && !_isAuthEndpoint(options.path)) {
    await _refreshTokenWithMutex();
    // Update request with new token
  }
  handler.next(options);
}
```

#### 🔄 **Reactive Refresh (Fallback Strategy)**
- **When**: After receiving 401 Unauthorized error
- **Trigger**: Token actually expired or invalid
- **Benefit**: Handles edge cases and clock skew
- **Implementation**: `onError()` interceptor method

```dart
@override
void onError(DioException err, ErrorInterceptorHandler handler) async {
  if (err.response?.statusCode == 401) {
    final success = await _refreshTokenWithMutex();
    if (success) {
      final retryResponse = await _retryRequest(err.requestOptions);
      handler.resolve(retryResponse);
    }
  }
}
```

**Interceptor Order**:
```dart
[
  LoggingInterceptor,
  RetryInterceptor,
  MockInterceptor,
  AuthInterceptor,        // Adds auth token
  TokenRefreshInterceptor, // Handles 401 and refreshes
  ErrorInterceptor,       // Final error handling
]
```

### Token Storage

**New Storage Methods**:
```dart
// Token expiry tracking
Future<void> saveTokenExpiryTime(DateTime expiryTime);
Future<DateTime?> getTokenExpiryTime();
Future<bool> isTokenExpired();
```

**Storage Keys**:
- `ACCESS_TOKEN` - Current access token
- `REFRESH_TOKEN` - Refresh token for getting new access token
- `TOKEN_EXPIRY_TIME` - Timestamp when access token expires

### API Integration

**Refresh Token Request**:
```dart
class RefreshTokenRequest {
  final String refreshToken;
  
  Map<String, dynamic> toJson() => {
    'refresh_token': refreshToken,
  };
}
```

**Refresh Token Response**:
```dart
class RefreshTokenResponse {
  final String accessToken;
  final String refreshToken;
  final int expiresIn;
}
```

## 🔄 Token Refresh Flow

### 1. Proactive Token Refresh (Before Request)
```dart
// TokenRefreshInterceptor checks token expiry before request
final isExpired = await _storageService.isTokenExpired();

// isTokenExpired() returns true if token expires within 30 seconds
if (isExpired && !_isAuthEndpoint(options.path)) {
  // Proactively refresh token before making request
  final refreshSuccess = await _refreshTokenWithMutex();

  if (refreshSuccess) {
    // Update request with new token
    options.headers['Authorization'] = 'Bearer new_token';
  }
}
```

### 2. Reactive Token Refresh (After 401 Error)
```dart
// If proactive refresh failed or token expired during request
// API returns 401 Unauthorized
```

### 3. Automatic Token Refresh
```dart
// TokenRefreshInterceptor detects 401
if (err.response?.statusCode == 401) {
  // Skip if auth endpoint (prevent infinite loop)
  if (_isAuthEndpoint(err.requestOptions.path)) return;
  
  // Attempt refresh with mutex for thread safety
  final success = await _refreshTokenWithMutex();
  
  if (success) {
    // Retry original request with new token
    final retryResponse = await _retryRequest(err.requestOptions);
    handler.resolve(retryResponse);
  }
}
```

### 3. Concurrent Request Handling
```dart
// Multiple requests with expired token
static Completer<void>? _currentRefreshCompleter;

// First request starts refresh
if (_currentRefreshCompleter == null) {
  _currentRefreshCompleter = Completer<void>();
  await _performTokenRefresh();
  _currentRefreshCompleter!.complete();
}

// Other requests wait for refresh to complete
else {
  await _currentRefreshCompleter!.future;
  // Check if refresh was successful
  final newToken = await _storageService.getAccessToken();
  return newToken != null && newToken.isNotEmpty;
}
```

## 🧪 Mock API Support

### Mock Refresh Endpoint

**Path**: `/auth/refresh`

**Mock Validation**:
- Validates refresh token format
- Generates new mock tokens
- Simulates real API behavior

**Mock Response**:
```json
{
  "access_token": "mock_new_access_token_1234567890",
  "refresh_token": "mock_new_refresh_token_1234567890", 
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

## ⚙️ Configuration

### Network Module Integration

```dart
@Named('default')
ApiClient defaultApiClient(
  AuthInterceptor authInterceptor,
  TokenRefreshInterceptor tokenRefreshInterceptor,
  // ... other interceptors
) {
  return ApiClient(
    interceptors: [
      loggingInterceptor,
      retryInterceptor,
      mockAuthInterceptor,
      authInterceptor,
      tokenRefreshInterceptor, // After auth, before error
      errorInterceptor,
    ],
  );
}
```

### Dependency Injection

The system is automatically registered through `@injectable` annotations:
- `TokenRefreshInterceptor` - Registered as singleton
- `AuthApiService` - Uses auth API client (no auth interceptor)
- `StorageService` - Provides token storage methods

## 🔒 Security Considerations

### Token Expiry Buffer (30 Seconds)
- **Proactive Refresh**: Tokens are refreshed 30 seconds before actual expiry
- **Prevents Race Conditions**: Avoids token expiring during request transmission
- **Clock Skew Protection**: Handles time differences between client and server
- **Network Latency Buffer**: Accounts for network delays
- **Smooth UX**: Users never experience authentication interruptions

**Implementation**:
```dart
Future<bool> isTokenExpired() async {
  final expiryTime = await getTokenExpiryTime();
  if (expiryTime == null) return true;

  // Add 30 seconds buffer to refresh before actual expiry
  final bufferTime = expiryTime.subtract(const Duration(seconds: 30));
  return DateTime.now().isAfter(bufferTime);
}
```

### Refresh Token Validation
- Refresh tokens are validated before use
- Invalid refresh tokens trigger immediate logout
- Secure storage using FlutterSecureStorage

### Endpoint Protection
- Auth endpoints (`/auth/*`) skip token refresh
- Prevents infinite refresh loops
- Maintains system stability

## 🚨 Error Handling

### Refresh Failure Scenarios

1. **No Refresh Token**:
   ```dart
   if (refreshToken == null || refreshToken.isEmpty) {
     AppLogger.warning('TokenRefresh: No refresh token available');
     return false;
   }
   ```

2. **Invalid Refresh Token**:
   ```dart
   // API returns 401 for refresh request
   result.fold(
     (failure) {
       AppLogger.error('TokenRefresh: API call failed', error: failure);
       return false;
     },
   ```

3. **Network Error During Refresh**:
   ```dart
   } catch (e) {
     AppLogger.error('TokenRefresh: Exception during refresh', error: e);
     return false;
   }
   ```

### Fallback Actions
- Clear all stored tokens
- Log user out automatically
- Redirect to login screen (TODO: implement navigation)

## 📊 Logging and Monitoring

### Debug Logs
```dart
AppLogger.info('TokenRefresh: 401 detected, attempting token refresh');
AppLogger.debug('TokenRefresh: Waiting for ongoing refresh');
AppLogger.info('TokenRefresh: Tokens refreshed successfully');
AppLogger.warning('TokenRefresh: 401 on auth endpoint, skipping refresh');
```

### Error Logs
```dart
AppLogger.error('TokenRefresh: API call failed', error: failure);
AppLogger.error('TokenRefresh: Exception during refresh', error: e);
AppLogger.error('TokenRefresh: Failed to retry request', error: e);
```

## 🧪 Testing

### Unit Testing
- Mock `StorageService` for token operations
- Mock `AuthApiService` for refresh API calls
- Test concurrent request scenarios
- Test error handling paths

### Integration Testing
- Test with real API endpoints
- Verify token refresh flow end-to-end
- Test with expired tokens
- Verify fallback to logout

### Mock Testing
- Use mock interceptor for development
- Test various refresh scenarios
- Simulate network failures
- Test token validation logic

## 🔮 Future Enhancements

### Proactive Token Refresh
- Background token refresh before expiry
- Scheduled refresh based on token lifetime
- Reduce 401 errors for better UX

### Advanced Error Handling
- Retry logic for refresh failures
- Exponential backoff for network errors
- User notification for auth issues

### Analytics Integration
- Track token refresh frequency
- Monitor refresh success rates
- Alert on unusual refresh patterns

## 📚 Related Documentation

- [Authentication Guide](./AUTHENTICATION_GUIDE.md)
- [Error Handling Guide](./ERROR_HANDLING_GUIDE.md)
- [Network Architecture](./NETWORK_ARCHITECTURE.md)
- [Mock API Guide](./MOCK_API_GUIDE.md)
