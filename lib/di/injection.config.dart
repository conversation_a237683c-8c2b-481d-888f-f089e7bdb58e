// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:connectivity_plus/connectivity_plus.dart' as _i895;
import 'package:dio/dio.dart' as _i361;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

import '../core/services/image_picker_service.dart' as _i735;
import '../data/datasources/local/user_local_datasource.dart' as _i358;
import '../data/datasources/local/user_local_datasource_impl.dart' as _i568;
import '../data/datasources/remote/auth_api_service.dart' as _i939;
import '../data/datasources/remote/product_api_service.dart' as _i21;
import '../data/network/api_client.dart' as _i1013;
import '../data/network/interceptors/auth_interceptor.dart' as _i808;
import '../data/network/interceptors/error_interceptor.dart' as _i454;
import '../data/network/interceptors/logging_interceptor.dart' as _i390;
import '../data/network/interceptors/mock/mock_auth_interceptor.dart' as _i555;
import '../data/network/interceptors/simple_retry_interceptor.dart' as _i1041;
import '../data/network/openapi_client.dart' as _i290;
import '../data/repositories_impl/auth_repository_impl.dart' as _i953;
import '../data/services_impl/database_service_impl.dart' as _i946;
import '../data/services_impl/environment_service_impl.dart' as _i308;
import '../data/services_impl/logger_service_impl.dart' as _i183;
import '../data/services_impl/network_info_impl.dart' as _i431;
import '../data/services_impl/storage_service_impl.dart' as _i366;
import '../domain/repositories/auth_repository.dart' as _i800;
import '../domain/services/database_service.dart' as _i744;
import '../domain/services/environment_service.dart' as _i429;
import '../domain/services/logger_service.dart' as _i482;
import '../domain/services/network_info.dart' as _i85;
import '../domain/services/storage_service.dart' as _i1022;
import 'injection.dart' as _i464;
import 'module.dart' as _i946;
import 'network_module.dart' as _i567;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    final appModule = _$AppModule();
    final networkModule = _$NetworkModule();
    await gh.factoryAsync<_i460.SharedPreferences>(
      () => registerModule.sharedPreferences,
      preResolve: true,
    );
    gh.factory<_i555.MockAuthInterceptor>(() => _i555.MockAuthInterceptor());
    gh.factory<_i454.ErrorInterceptor>(() => _i454.ErrorInterceptor());
    gh.singleton<_i895.Connectivity>(() => appModule.connectivity);
    gh.singleton<_i735.ImagePickerService>(() => _i735.ImagePickerService());
    gh.lazySingleton<_i558.FlutterSecureStorage>(
        () => registerModule.secureStorage);
    gh.lazySingleton<_i482.LoggerService>(() => _i183.LoggerServiceImpl());
    gh.factory<_i390.LoggingInterceptor>(
        () => _i390.LoggingInterceptor(gh<_i482.LoggerService>()));
    gh.lazySingleton<_i358.UserLocalDataSource>(
        () => _i568.UserLocalDataSourceImpl());
    gh.factory<_i1041.SimpleRetryInterceptor>(
        () => _i1041.SimpleRetryInterceptor(
              maxRetries: gh<int>(),
              initialDelay: gh<Duration>(),
              enableLogging: gh<bool>(),
            ));
    gh.lazySingleton<_i1022.StorageService>(() => _i366.StorageServiceImpl(
          gh<_i460.SharedPreferences>(),
          gh<_i558.FlutterSecureStorage>(),
        ));
    gh.lazySingleton<_i744.DatabaseService>(
        () => _i946.DatabaseServiceImpl(gh<_i358.UserLocalDataSource>()));
    gh.factory<_i85.NetworkInfo>(
        () => _i431.NetworkInfoImpl(gh<_i895.Connectivity>()));
    gh.factory<_i808.AuthInterceptor>(
        () => _i808.AuthInterceptor(gh<_i1022.StorageService>()));
    gh.lazySingleton<_i429.EnvironmentService>(
        () => _i308.EnvironmentServiceImpl(gh<_i1022.StorageService>()));
    gh.singleton<_i361.Dio>(() => networkModule.legacyDio(
          gh<_i808.AuthInterceptor>(),
          gh<_i390.LoggingInterceptor>(),
          gh<_i454.ErrorInterceptor>(),
          gh<_i555.MockAuthInterceptor>(),
          gh<_i429.EnvironmentService>(),
        ));
    gh.singleton<_i1013.ApiClient>(
      () => networkModule.authApiClient(
        gh<_i390.LoggingInterceptor>(),
        gh<_i454.ErrorInterceptor>(),
        gh<_i555.MockAuthInterceptor>(),
        gh<_i429.EnvironmentService>(),
      ),
      instanceName: 'auth',
    );
    gh.singleton<_i1013.ApiClient>(
      () => networkModule.uploadApiClient(
        gh<_i808.AuthInterceptor>(),
        gh<_i390.LoggingInterceptor>(),
        gh<_i454.ErrorInterceptor>(),
        gh<_i555.MockAuthInterceptor>(),
        gh<_i429.EnvironmentService>(),
      ),
      instanceName: 'upload',
    );
    gh.singleton<_i1013.ApiClient>(
      () => networkModule.defaultApiClient(
        gh<_i808.AuthInterceptor>(),
        gh<_i390.LoggingInterceptor>(),
        gh<_i454.ErrorInterceptor>(),
        gh<_i555.MockAuthInterceptor>(),
        gh<_i429.EnvironmentService>(),
      ),
      instanceName: 'default',
    );
    gh.singleton<_i1013.ApiClient>(
      () => networkModule.publicApiClient(
        gh<_i390.LoggingInterceptor>(),
        gh<_i454.ErrorInterceptor>(),
        gh<_i429.EnvironmentService>(),
      ),
      instanceName: 'public',
    );
    gh.singleton<_i290.OpenApiClient>(() => _i290.OpenApiClient(
          gh<_i1013.ApiClient>(instanceName: 'default'),
          gh<_i1013.ApiClient>(instanceName: 'auth'),
          gh<_i1013.ApiClient>(instanceName: 'upload'),
        ));
    gh.factory<_i939.AuthApiService>(
        () => _i939.AuthApiService(gh<_i1013.ApiClient>(instanceName: 'auth')));
    gh.factory<_i800.AuthRepository>(() => _i953.AuthRepositoryImpl(
          gh<_i939.AuthApiService>(),
          gh<_i1022.StorageService>(),
          gh<_i744.DatabaseService>(),
          gh<_i85.NetworkInfo>(),
        ));
    gh.factory<_i21.ProductApiService>(() =>
        _i21.ProductApiService(gh<_i1013.ApiClient>(instanceName: 'default')));
    return this;
  }
}

class _$RegisterModule extends _i464.RegisterModule {}

class _$AppModule extends _i946.AppModule {}

class _$NetworkModule extends _i567.NetworkModule {}
