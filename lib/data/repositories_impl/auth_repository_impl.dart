import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/domain/entities/user.dart';
import 'package:sales_app/domain/repositories/auth_repository.dart';
import 'package:sales_app/domain/services/storage_service.dart';
import 'package:sales_app/domain/services/database_service.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/data/datasources/remote/auth_api_service.dart';
import 'package:sales_app/core/utils/app_logger.dart';

@Injectable(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _authApiService;
  final StorageService _storageService;
  final DatabaseService _databaseService;
  final NetworkInfo _networkInfo;

  AuthRepositoryImpl(
    this._authApiService,
    this._storageService,
    this._databaseService,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  }) async {
    AppLogger.info('Attempting login', data: {'email': email});

    // Check network connectivity
    final isConnected = await _networkInfo.isConnected;

    if (isConnected) {
      // Online login
      return await _loginOnline(email, password);
    } else {
      // Offline login - check local database
      return await _loginOffline(email, password);
    }
  }

  /// Online login with API
  Future<Either<Failure, User>> _loginOnline(
      String email, String password) async {
    try {
      final request = LoginRequest(email: email, password: password);
      final result = await _authApiService.login(request);

      return result.fold(
        (failure) async {
          AppLogger.error('Online login failed', error: failure);

          // Fallback to offline login if API fails
          AppLogger.info('Attempting offline login fallback');
          return await _loginOffline(email, password);
        },
        (loginResponse) async {
          final user = loginResponse.user.toEntity();

          // Store tokens
          await _storageService.saveAccessToken(loginResponse.accessToken);
          await _storageService.saveRefreshToken(loginResponse.refreshToken);

          // Calculate and save token expiry time (assuming 1 hour from now)
          final expiryTime = DateTime.now().add(const Duration(hours: 1));
          await _storageService.saveTokenExpiryTime(expiryTime);

          // Save user to local database for offline access
          await _databaseService.saveUser(user);

          AppLogger.info('Online login successful', data: {
            'userId': user.id,
            'email': user.email,
          });

          return Right(user);
        },
      );
    } catch (e) {
      AppLogger.error('Online login exception', error: e);
      return await _loginOffline(email, password);
    }
  }

  /// Offline login using local database
  Future<Either<Failure, User>> _loginOffline(
      String email, String password) async {
    try {
      // Check if user exists in local database
      final localUser = await _databaseService.getUserByEmail(email);

      if (localUser == null) {
        AppLogger.warning('Offline login failed - user not found',
            data: {'email': email});
        return const Left(AuthFailure(AuthErrorType.userNotFound));
      }

      // Note: In real app, you should store password hash for offline verification
      // For now, we'll allow offline login if user exists locally
      AppLogger.info('Offline login successful', data: {
        'userId': localUser.id,
        'email': localUser.email,
      });

      return Right(localUser);
    } catch (e) {
      AppLogger.error('Offline login exception', error: e);
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi đăng nhập offline: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    AppLogger.info('Attempting logout');

    // Try to logout from API if online
    final isConnected = await _networkInfo.isConnected;
    Either<Failure, void> result = const Right(null);

    if (isConnected) {
      result = await _authApiService.logout();
      if (result.isLeft()) {
        AppLogger.warning('API logout failed, continuing with local logout');
      }
    } else {
      AppLogger.info('Offline logout - skipping API call');
    }

    // Clear all local data regardless of API result
    await _clearAllAuthData();

    AppLogger.info('Logout completed');
    return const Right(null);
  }

  @override
  Future<bool> isLoggedIn() async {
    // Check both token and local user data
    final token = await _storageService.getAccessToken();
    final hasToken = token != null && token.isNotEmpty;

    // Also check if we have user data locally (for offline support)
    final usersCount = await _databaseService.getUsersCount();
    final hasLocalUser = usersCount > 0;

    AppLogger.debug('Login status check', data: {
      'hasToken': hasToken,
      'hasLocalUser': hasLocalUser,
    });

    return hasToken || hasLocalUser;
  }

  /// Clear all authentication data (tokens + local database)
  Future<void> _clearAllAuthData() async {
    try {
      await Future.wait([
        _storageService.clearTokens(),
        _databaseService.clearAllUsers(),
      ]);
      AppLogger.info('All auth data cleared');
    } catch (e) {
      AppLogger.error('Failed to clear auth data', error: e);
    }
  }
}
