import 'dart:async';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/core/constants/api_paths.dart';
import 'package:sales_app/domain/services/storage_service.dart';
import 'package:sales_app/data/datasources/remote/auth_api_service.dart';

/// Token Refresh Interceptor
/// Handles automatic token refresh when access token expires (401 errors)
/// Ensures thread-safety for concurrent requests during token refresh
@injectable
class TokenRefreshInterceptor extends Interceptor {
  final StorageService _storageService;
  final AuthApiService _authApiService;
  
  // Mutex to prevent concurrent refresh attempts
  static Completer<void>? _currentRefreshCompleter;
  
  TokenRefreshInterceptor(
    this._storageService,
    this._authApiService,
  );

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Only handle 401 Unauthorized errors
    if (err.response?.statusCode != 401) {
      handler.next(err);
      return;
    }

    // Skip refresh for auth endpoints to avoid infinite loops
    if (_isAuthEndpoint(err.requestOptions.path)) {
      AppLogger.warning('TokenRefresh: 401 on auth endpoint, skipping refresh');
      await _handleRefreshFailure();
      handler.next(err);
      return;
    }

    AppLogger.info('TokenRefresh: 401 detected, attempting token refresh');

    try {
      // Attempt to refresh token
      final refreshSuccess = await _refreshTokenWithMutex();
      
      if (refreshSuccess) {
        // Retry the original request with new token
        final retryResponse = await _retryRequest(err.requestOptions);
        handler.resolve(retryResponse);
      } else {
        // Refresh failed, proceed with original error
        await _handleRefreshFailure();
        handler.next(err);
      }
    } catch (e) {
      AppLogger.error('TokenRefresh: Unexpected error during refresh', error: e);
      await _handleRefreshFailure();
      handler.next(err);
    }
  }

  /// Refresh token with mutex to handle concurrent requests
  Future<bool> _refreshTokenWithMutex() async {
    // If refresh is already in progress, wait for it
    if (_currentRefreshCompleter != null) {
      AppLogger.debug('TokenRefresh: Waiting for ongoing refresh');
      await _currentRefreshCompleter!.future;
      
      // Check if refresh was successful by verifying new token
      final newToken = await _storageService.getAccessToken();
      return newToken != null && newToken.isNotEmpty;
    }

    // Start new refresh process
    _currentRefreshCompleter = Completer<void>();
    
    try {
      final success = await _performTokenRefresh();
      _currentRefreshCompleter!.complete();
      return success;
    } catch (e) {
      _currentRefreshCompleter!.completeError(e);
      rethrow;
    } finally {
      _currentRefreshCompleter = null;
    }
  }

  /// Perform the actual token refresh
  Future<bool> _performTokenRefresh() async {
    try {
      final refreshToken = await _storageService.getRefreshToken();
      
      if (refreshToken == null || refreshToken.isEmpty) {
        AppLogger.warning('TokenRefresh: No refresh token available');
        return false;
      }

      AppLogger.debug('TokenRefresh: Calling refresh API');
      final request = RefreshTokenRequest(refreshToken: refreshToken);
      final result = await _authApiService.refreshToken(request);

      return result.fold(
        (failure) {
          AppLogger.error('TokenRefresh: API call failed', error: failure);
          return false;
        },
        (response) async {
          // Save new tokens
          await _storageService.saveAccessToken(response.accessToken);
          await _storageService.saveRefreshToken(response.refreshToken);

          // Calculate and save new token expiry time
          final expiryTime = DateTime.now().add(Duration(seconds: response.expiresIn));
          await _storageService.saveTokenExpiryTime(expiryTime);

          AppLogger.info('TokenRefresh: Tokens refreshed successfully');
          return true;
        },
      );
    } catch (e) {
      AppLogger.error('TokenRefresh: Exception during refresh', error: e);
      return false;
    }
  }

  /// Retry the original request with new access token
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    try {
      // Get the new access token
      final newToken = await _storageService.getAccessToken();
      
      // Update authorization header
      if (newToken != null && newToken.isNotEmpty) {
        requestOptions.headers['Authorization'] = 'Bearer $newToken';
      }

      AppLogger.debug('TokenRefresh: Retrying original request');
      
      // Create new Dio instance to avoid interceptor loops
      final dio = Dio();
      return await dio.fetch(requestOptions);
    } catch (e) {
      AppLogger.error('TokenRefresh: Failed to retry request', error: e);
      rethrow;
    }
  }

  /// Handle refresh failure by clearing tokens
  Future<void> _handleRefreshFailure() async {
    try {
      AppLogger.info('TokenRefresh: Clearing tokens due to refresh failure');
      await _storageService.clearTokens();
      
      // TODO: Trigger logout event or navigate to login
      // This could be done through a global event bus or navigation service
    } catch (e) {
      AppLogger.error('TokenRefresh: Error clearing tokens', error: e);
    }
  }

  /// Check if the request is to an auth endpoint
  bool _isAuthEndpoint(String path) {
    return ApiPaths.isAuthPath(path);
  }
}
